#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调用模板-微信自动化系统 - "操作过于频繁"错误检测和处理模块（重构版）
核心功能：
    1. 智能检测"操作过于频繁"错误提示
    2. 自动处理错误窗口（点击确定、关闭窗口）
    3. 提供详细的错误报告和日志记录

重构说明：
    - 移除了智能窗口切换功能
    - 移除了与window_manager.py和main_interface.py的集成
    - 移除了demo_auto_handling.py相关调用
    - 专注于核心错误检测和处理功能
    - 保持代码结构清晰，移除冗余代码

作者：AI助手
创建时间：2025-07-26
重构时间：2025-07-28
版本：v2.0.0（重构版）
"""

import time
import logging
import win32gui
import win32con
import win32api
import win32process
import pyautogui
import traceback
import threading
import psutil
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
import sys
import os

# 添加modules目录到Python路径，确保导入正常工作
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)


@dataclass
class WindowInfo:
    """窗口信息数据类"""
    hwnd: int
    title: str
    class_name: str
    rect: Tuple[int, int, int, int]
    is_visible: bool
    is_enabled: bool


@dataclass
class ErrorDetectionResult:
    """错误检测结果数据类"""
    has_error: bool
    error_type: str
    error_message: str
    window_info: Optional[WindowInfo]
    detection_time: float
    confidence: float  # 检测置信度 0.0-1.0


class FrequencyErrorHandler:
    """微信"操作过于频繁"错误检测和处理器

    核心功能：
    1. 智能检测"操作过于频繁"错误提示
    2. 自动处理错误窗口（点击确定、关闭窗口）
    3. 提供详细的错误报告和日志记录

    重构说明：
    - 已移除智能窗口切换功能
    - 已移除与window_manager.py和main_interface.py的集成
    - 专注于核心错误检测和处理功能
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """初始化频率错误处理器
        
        Args:
            logger: 日志记录器，如果为None则创建新的
        """
        self.logger = logger or self._setup_logger()
        
        # 错误检测配置
        self.error_patterns = {
            "too_frequent": [
                "操作过于频繁",
                "操作太频繁", 
                "请稍后再试",
                "添加频繁",
                "操作频繁",
                "频繁操作",
                "稍后再试"
            ],
            "network_error": [
                "网络连接失败",
                "网络异常", 
                "连接超时",
                "网络错误"
            ],
            "permission_denied": [
                "权限不足",
                "无权限操作",
                "没有权限"
            ]
        }
        
        # 🔧 窗口检测配置（精确版 - 防止误识别其他应用程序）
        self.target_windows = {
            "add_friend": {
                "class_names": ["Qt51514QWindowIcon", "WeChatMainWndForPC"],
                "title_patterns": ["添加朋友"],  # 🔧 只匹配添加朋友窗口，避免误识别
                "size_range": {"min_width": 300, "max_width": 800, "min_height": 150, "max_height": 600},
                "exclude_sizes": [(330, 194)],  # 排除错误提示窗口大小
                "exclude_class_names": ["Chrome_WidgetWin_1", "Notepad", "ApplicationFrameWindow"]  # 🆕 排除其他应用
            },
            "main_wechat": {
                "class_names": ["Qt51514QWindowIcon", "WeChatMainWndForPC"],
                "title_patterns": [],  # 🔧 不使用标题匹配，只使用类名和大小
                "size_range": {"min_width": 300, "max_width": 1000, "min_height": 150, "max_height": 800},
                "exclude_class_names": ["Chrome_WidgetWin_1", "Notepad", "ApplicationFrameWindow"],  # 🆕 排除其他应用
                "require_exact_class": True  # 🆕 要求精确的类名匹配
            },
            "error_dialog": {
                "class_names": ["Qt51514QWindowIcon", "#32770", "Dialog"],
                "title_patterns": ["提示", "错误", "警告"],  # 🔧 移除"微信"避免误识别
                "size_range": {"min_width": 250, "max_width": 500, "min_height": 100, "max_height": 300},
                "exclude_class_names": ["Chrome_WidgetWin_1", "Notepad", "ApplicationFrameWindow"]  # 🆕 排除其他应用
            }
        }
        
        # 检测参数
        self.detection_timeout = 10.0  # 检测超时时间（秒）
        self.retry_attempts = 3  # 重试次数
        self.click_delay = 1.0  # 点击操作间延迟
        self.window_switch_delay = 2.0  # 窗口切换延迟
        
        # 状态跟踪
        self.last_detection_time = 0
        self.detection_history: List[ErrorDetectionResult] = []
        self.max_history_size = 50

        # 线程安全锁
        self._lock = threading.Lock()

        # 重新开始标志
        self._restart_required = False

        # 🆕 程序终止标志
        self._terminate_required = False

        # 🆕 窗口状态跟踪（用于管理哪些微信窗口出现了频繁错误）
        self.window_error_status = {}  # {window_hwnd: {"title": str, "error_count": int, "last_error_time": float}}
        self.max_error_count_per_window = 3  # 每个窗口最大错误次数
        self.error_cooldown_time = 300  # 错误冷却时间（秒）

        self.logger.info("✅ 频率错误处理器初始化完成")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(f"{__name__}.FrequencyErrorHandler")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def detect_frequency_error(self, timeout: Optional[float] = None) -> ErrorDetectionResult:
        """检测"操作过于频繁"错误（兼容方法）

        Args:
            timeout: 检测超时时间，默认使用配置值

        Returns:
            ErrorDetectionResult: 检测结果
        """
        return self.detect_frequency_error_after_click(timeout)

    def detect_frequency_error_for_window(self, window_hwnd: int, window_title: str, timeout: Optional[float] = None) -> ErrorDetectionResult:
        """针对特定窗口检测"操作过于频繁"错误

        这是窗口独立检测方法，只检测指定窗口的频率错误

        Args:
            window_hwnd: 目标窗口句柄
            window_title: 目标窗口标题
            timeout: 检测超时时间，默认使用配置值

        Returns:
            ErrorDetectionResult: 检测结果
        """
        timeout = timeout or self.detection_timeout
        start_time = time.time()

        self.logger.info(f"🔍 开始检测窗口 {window_title} (句柄: {window_hwnd}) 的频率错误...")
        self.logger.info(f"⏱️ 检测超时时间: {timeout}秒")

        try:
            # 等待窗口响应
            time.sleep(0.5)

            while time.time() - start_time < timeout:
                # 🆕 只检测与指定窗口相关的错误对话框
                dialog_result = self._check_error_dialog_for_specific_window(window_hwnd, window_title)
                if dialog_result.has_error:
                    self.logger.info(f"⚠️ 在窗口 {window_title} 中检测到错误对话框")
                    return dialog_result

                # 🆕 检测指定窗口的添加朋友窗口错误
                add_friend_result = self._check_add_friend_window_for_specific_window(window_hwnd, window_title)
                if add_friend_result.has_error:
                    self.logger.info(f"📱 在窗口 {window_title} 的添加朋友窗口中检测到错误")
                    return add_friend_result

                # 🆕 检测指定的主微信窗口
                main_window_result = self._check_specific_main_wechat_window(window_hwnd, window_title)
                if main_window_result.has_error:
                    self.logger.info(f"🏠 在窗口 {window_title} 中检测到错误")
                    return main_window_result

                # 短暂等待后继续检测
                time.sleep(0.2)

            # 超时未检测到错误
            self.logger.info(f"✅ 窗口 {window_title} 检测完成，未发现频率错误")
            return ErrorDetectionResult(
                has_error=False,
                error_type="none",
                error_message=f"窗口 {window_title} 未检测到错误",
                window_info=None,
                detection_time=time.time() - start_time,
                confidence=1.0
            )

        except Exception as e:
            self.logger.error(f"❌ 检测窗口 {window_title} 错误异常: {e}")
            self.logger.error(traceback.format_exc())
            return ErrorDetectionResult(
                has_error=False,
                error_type="detection_error",
                error_message=f"窗口 {window_title} 检测异常: {str(e)}",
                window_info=None,
                detection_time=time.time() - start_time,
                confidence=0.0
            )

    def detect_frequency_error_after_click(self, timeout: Optional[float] = None) -> ErrorDetectionResult:
        """在点击确定按钮后检测"操作过于频繁"错误

        这是核心检测方法，在点击"确定"按钮后立即调用

        Args:
            timeout: 检测超时时间，默认使用配置值

        Returns:
            ErrorDetectionResult: 检测结果
        """
        timeout = timeout or self.detection_timeout
        start_time = time.time()

        self.logger.info("🔍 开始检测'操作过于频繁'错误...")
        self.logger.info(f"⏱️ 检测超时时间: {timeout}秒")

        try:
            # 等待窗口响应
            time.sleep(0.5)

            while time.time() - start_time < timeout:
                # 🆕 优先检测错误对话框（基于截图分析）
                dialog_result = self._check_error_dialog_enhanced()
                if dialog_result.has_error:
                    self.logger.info("⚠️ 检测到错误对话框")
                    return dialog_result

                # 检测添加朋友窗口
                add_friend_result = self._check_add_friend_window()
                if add_friend_result.has_error:
                    self.logger.info("📱 在添加朋友窗口中检测到错误")
                    return add_friend_result

                # 检测主微信窗口
                main_window_result = self._check_main_wechat_window()
                if main_window_result.has_error:
                    self.logger.info("🏠 在主微信窗口中检测到错误")
                    return main_window_result

                # 短暂等待后继续检测
                time.sleep(0.2)

            # 超时未检测到错误
            self.logger.info("✅ 检测完成，未发现'操作过于频繁'错误")
            return ErrorDetectionResult(
                has_error=False,
                error_type="none",
                error_message="未检测到错误",
                window_info=None,
                detection_time=time.time() - start_time,
                confidence=1.0
            )

        except Exception as e:
            self.logger.error(f"❌ 错误检测异常: {e}")
            self.logger.error(traceback.format_exc())
            return ErrorDetectionResult(
                has_error=False,
                error_type="detection_error",
                error_message=f"检测异常: {str(e)}",
                window_info=None,
                detection_time=time.time() - start_time,
                confidence=0.0
            )
    
    def _check_add_friend_window(self) -> ErrorDetectionResult:
        """检查添加朋友窗口中的错误信息"""
        try:
            windows = self._find_windows_by_criteria("add_friend")
            
            for window_info in windows:
                # 获取窗口文本内容
                text_content = self._extract_window_text(window_info.hwnd)
                
                # 检查是否包含错误信息
                error_result = self._analyze_text_for_errors(text_content, window_info)
                if error_result.has_error:
                    return error_result
            
            return self._create_no_error_result()
            
        except Exception as e:
            self.logger.error(f"❌ 检查添加朋友窗口异常: {e}")
            return self._create_no_error_result()
    
    def _check_main_wechat_window(self) -> ErrorDetectionResult:
        """检查主微信窗口中的错误信息"""
        try:
            windows = self._find_windows_by_criteria("main_wechat")
            
            for window_info in windows:
                # 特别检查指定大小的微信窗口 (330x194)
                width = window_info.rect[2] - window_info.rect[0]
                height = window_info.rect[3] - window_info.rect[1]
                
                if width == 330 and height == 194:
                    self.logger.info(f"🎯 检测到目标微信窗口 (330x194): {window_info.title}")
                    
                    # 获取窗口文本内容
                    text_content = self._extract_window_text(window_info.hwnd)
                    
                    # 检查是否包含错误信息
                    error_result = self._analyze_text_for_errors(text_content, window_info)
                    if error_result.has_error:
                        return error_result
            
            return self._create_no_error_result()
            
        except Exception as e:
            self.logger.error(f"❌ 检查主微信窗口异常: {e}")
            return self._create_no_error_result()
    
    def _check_error_dialog(self) -> ErrorDetectionResult:
        """检查错误对话框"""
        try:
            windows = self._find_windows_by_criteria("error_dialog")

            for window_info in windows:
                # 获取窗口文本内容
                text_content = self._extract_window_text(window_info.hwnd)

                # 检查是否包含错误信息
                error_result = self._analyze_text_for_errors(text_content, window_info)
                if error_result.has_error:
                    return error_result

            return self._create_no_error_result()

        except Exception as e:
            self.logger.error(f"❌ 检查错误对话框异常: {e}")
            return self._create_no_error_result()

    def _check_error_dialog_for_specific_window(self, target_hwnd: int, target_title: str) -> ErrorDetectionResult:
        """检查与特定窗口相关的错误对话框"""
        try:
            self.logger.info(f"🔍 检查窗口 {target_title} (句柄: {target_hwnd}) 的错误对话框...")

            # 获取所有可能的错误对话框
            windows = self._find_windows_by_criteria("error_dialog")

            for window_info in windows:
                # 🆕 检查是否与目标窗口相关（通过位置、进程等关联）
                if self._is_dialog_related_to_window(window_info, target_hwnd, target_title):
                    self.logger.info(f"🎯 找到与窗口 {target_title} 相关的错误对话框")

                    # 获取窗口文本内容
                    text_content = self._extract_window_text(window_info.hwnd)

                    # 检查是否包含错误信息
                    error_result = self._analyze_text_for_errors(text_content, window_info)
                    if error_result.has_error:
                        self.logger.info(f"✅ 在窗口 {target_title} 的对话框中发现频率错误")
                        return error_result

            return self._create_no_error_result()

        except Exception as e:
            self.logger.error(f"❌ 检查窗口 {target_title} 错误对话框异常: {e}")
            return self._create_no_error_result()

    def _check_add_friend_window_for_specific_window(self, target_hwnd: int, target_title: str) -> ErrorDetectionResult:
        """检查与特定微信窗口相关的添加朋友窗口错误"""
        try:
            self.logger.info(f"🔍 检查窗口 {target_title} 的添加朋友窗口...")

            # 获取所有添加朋友窗口
            windows = self._find_windows_by_criteria("add_friend")

            for window_info in windows:
                # 🆕 检查是否与目标微信窗口相关
                if self._is_add_friend_related_to_wechat_window(window_info, target_hwnd, target_title):
                    self.logger.info(f"🎯 找到与窗口 {target_title} 相关的添加朋友窗口")

                    # 获取窗口文本内容
                    text_content = self._extract_window_text(window_info.hwnd)

                    # 检查是否包含错误信息
                    error_result = self._analyze_text_for_errors(text_content, window_info)
                    if error_result.has_error:
                        self.logger.info(f"✅ 在窗口 {target_title} 的添加朋友窗口中发现频率错误")
                        return error_result

            return self._create_no_error_result()

        except Exception as e:
            self.logger.error(f"❌ 检查窗口 {target_title} 添加朋友窗口异常: {e}")
            return self._create_no_error_result()

    def _check_specific_main_wechat_window(self, target_hwnd: int, target_title: str) -> ErrorDetectionResult:
        """检查指定的主微信窗口中的错误信息"""
        try:
            self.logger.info(f"🔍 检查指定的微信主窗口 {target_title} (句柄: {target_hwnd})...")

            # 🆕 直接检查目标窗口
            import win32gui

            # 验证窗口是否存在且可见
            if not win32gui.IsWindow(target_hwnd) or not win32gui.IsWindowVisible(target_hwnd):
                self.logger.warning(f"⚠️ 目标窗口 {target_title} 不存在或不可见")
                return self._create_no_error_result()

            # 获取窗口信息
            try:
                window_title = win32gui.GetWindowText(target_hwnd)
                class_name = win32gui.GetClassName(target_hwnd)
                rect = win32gui.GetWindowRect(target_hwnd)

                window_info = WindowInfo(
                    hwnd=target_hwnd,
                    title=window_title,
                    class_name=class_name,
                    rect=rect
                )

                self.logger.info(f"🎯 检查窗口: {window_title} ({class_name})")

                # 特别检查指定大小的微信窗口 (330x194)
                width = rect[2] - rect[0]
                height = rect[3] - rect[1]

                if width == 330 and height == 194:
                    self.logger.info(f"🎯 检测到目标微信错误窗口 (330x194): {window_title}")

                    # 获取窗口文本内容
                    text_content = self._extract_window_text(target_hwnd)

                    # 检查是否包含错误信息
                    error_result = self._analyze_text_for_errors(text_content, window_info)
                    if error_result.has_error:
                        self.logger.info(f"✅ 在窗口 {target_title} 中发现频率错误")
                        return error_result

            except Exception as e:
                self.logger.error(f"❌ 获取窗口 {target_hwnd} 信息异常: {e}")

            return self._create_no_error_result()

        except Exception as e:
            self.logger.error(f"❌ 检查指定微信窗口 {target_title} 异常: {e}")
            return self._create_no_error_result()

    def _is_dialog_related_to_window(self, dialog_window: WindowInfo, target_hwnd: int, target_title: str) -> bool:
        """判断错误对话框是否与目标窗口相关"""
        try:
            # 方法1: 检查窗口位置关系（错误对话框通常在主窗口附近）
            import win32gui

            if win32gui.IsWindow(target_hwnd):
                target_rect = win32gui.GetWindowRect(target_hwnd)
                dialog_rect = dialog_window.rect

                # 计算窗口中心点距离
                target_center_x = (target_rect[0] + target_rect[2]) // 2
                target_center_y = (target_rect[1] + target_rect[3]) // 2
                dialog_center_x = (dialog_rect[0] + dialog_rect[2]) // 2
                dialog_center_y = (dialog_rect[1] + dialog_rect[3]) // 2

                distance = ((target_center_x - dialog_center_x) ** 2 + (target_center_y - dialog_center_y) ** 2) ** 0.5

                # 如果距离在合理范围内（比如500像素内），认为相关
                if distance <= 500:
                    self.logger.info(f"✅ 对话框与窗口 {target_title} 位置相关，距离: {distance:.1f}px")
                    return True

            # 方法2: 检查进程关联（同一进程的窗口）
            try:
                import win32process
                target_pid = win32process.GetWindowThreadProcessId(target_hwnd)[1]
                dialog_pid = win32process.GetWindowThreadProcessId(dialog_window.hwnd)[1]

                if target_pid == dialog_pid:
                    self.logger.info(f"✅ 对话框与窗口 {target_title} 进程相关，PID: {target_pid}")
                    return True
            except:
                pass

            # 方法3: 检查窗口特征（微信错误对话框的特征）
            if (dialog_window.class_name == "Qt51514QWindowIcon" and
                "Weixin" in dialog_window.title):
                self.logger.info(f"✅ 对话框符合微信错误窗口特征")
                return True

            return False

        except Exception as e:
            self.logger.error(f"❌ 判断对话框关联性异常: {e}")
            return False

    def _is_add_friend_related_to_wechat_window(self, add_friend_window: WindowInfo, target_hwnd: int, target_title: str) -> bool:
        """判断添加朋友窗口是否与目标微信窗口相关"""
        try:
            # 方法1: 检查进程关联
            try:
                import win32process
                target_pid = win32process.GetWindowThreadProcessId(target_hwnd)[1]
                add_friend_pid = win32process.GetWindowThreadProcessId(add_friend_window.hwnd)[1]

                if target_pid == add_friend_pid:
                    self.logger.info(f"✅ 添加朋友窗口与窗口 {target_title} 进程相关，PID: {target_pid}")
                    return True
            except:
                pass

            # 方法2: 检查窗口标题特征
            if "添加朋友" in add_friend_window.title:
                self.logger.info(f"✅ 找到添加朋友窗口: {add_friend_window.title}")
                return True

            return False

        except Exception as e:
            self.logger.error(f"❌ 判断添加朋友窗口关联性异常: {e}")
            return False

    def _check_error_dialog_enhanced(self) -> ErrorDetectionResult:
        """增强的错误对话框检测 - 基于窗口特征识别微信错误提示"""
        try:
            found_windows = []

            def enum_windows_callback(hwnd, _):
                try:
                    if not win32gui.IsWindowVisible(hwnd):
                        return True

                    title = win32gui.GetWindowText(hwnd)
                    class_name = win32gui.GetClassName(hwnd)
                    rect = win32gui.GetWindowRect(hwnd)

                    # 检查窗口特征
                    width = rect[2] - rect[0]
                    height = rect[3] - rect[1]

                    # 🎯 基于实际测试的精确窗口特征匹配
                    is_wechat_error_dialog = (
                        # 精确匹配微信错误提示窗口特征
                        title == "Weixin" and
                        class_name == "Qt51514QWindowIcon" and
                        width == 330 and height == 194
                    )

                    if is_wechat_error_dialog:
                        self.logger.info(f"🎯 发现疑似微信错误提示窗口: {title} ({width}x{height})")

                        window_info = WindowInfo(
                            hwnd=hwnd,
                            title=title or "",
                            class_name=class_name or "",
                            rect=rect,
                            is_visible=True,
                            is_enabled=bool(win32gui.IsWindowEnabled(hwnd))
                        )
                        found_windows.append(window_info)

                except:
                    pass
                return True

            win32gui.EnumWindows(enum_windows_callback, None)

            # 检查找到的窗口
            for window_info in found_windows:
                self.logger.info(f"🔍 分析微信错误窗口: {window_info.title}")

                # 🆕 基于窗口特征的错误检测（不依赖文本提取）
                confidence = self._calculate_error_probability_by_window_features(window_info)

                if confidence > 0.7:  # 如果置信度超过70%
                    self.logger.info(f"🚨 基于窗口特征检测到错误，置信度: {confidence:.2f}")

                    return ErrorDetectionResult(
                        has_error=True,
                        error_type="too_frequent",
                        error_message="基于窗口特征检测到'操作过于频繁'错误",
                        window_info=window_info,
                        detection_time=time.time(),
                        confidence=confidence
                    )

                # 备用：尝试文本提取
                text_content = self._extract_window_text_enhanced(window_info.hwnd)
                if text_content:
                    error_result = self._analyze_text_for_errors(text_content, window_info)
                    if error_result.has_error:
                        self.logger.info(f"🎯 通过文本分析发现错误: {error_result.error_message}")
                        return error_result

            return self._create_no_error_result()

        except Exception as e:
            self.logger.error(f"❌ 增强错误对话框检测异常: {e}")
            return self._create_no_error_result()

    def _calculate_error_probability_by_window_features(self, window_info: WindowInfo) -> float:
        """基于窗口特征计算错误概率"""
        try:
            confidence = 0.0

            # 特征1: 精确的窗口大小匹配 (330x194)
            width = window_info.rect[2] - window_info.rect[0]
            height = window_info.rect[3] - window_info.rect[1]

            if width == 330 and height == 194:
                confidence += 0.4  # 40%权重
                self.logger.info(f"✅ 窗口大小匹配 (330x194): +0.4")

            # 特征2: 窗口标题匹配
            if window_info.title == "Weixin":
                confidence += 0.3  # 30%权重
                self.logger.info(f"✅ 窗口标题匹配 (Weixin): +0.3")

            # 特征3: 窗口类名匹配
            if window_info.class_name == "Qt51514QWindowIcon":
                confidence += 0.2  # 20%权重
                self.logger.info(f"✅ 窗口类名匹配 (Qt51514QWindowIcon): +0.2")

            # 特征4: 窗口位置特征（错误提示通常在屏幕中央或特定位置）
            screen_width = 1920  # 假设的屏幕宽度，可以动态获取
            screen_height = 1080  # 假设的屏幕高度

            window_center_x = (window_info.rect[0] + window_info.rect[2]) // 2
            window_center_y = (window_info.rect[1] + window_info.rect[3]) // 2

            # 如果窗口在屏幕中央附近
            if abs(window_center_x - screen_width // 2) < 200 and \
               abs(window_center_y - screen_height // 2) < 200:
                confidence += 0.1  # 10%权重
                self.logger.info(f"✅ 窗口位置合理 (中央附近): +0.1")

            self.logger.info(f"📊 窗口特征分析完成，总置信度: {confidence:.2f}")
            return confidence

        except Exception as e:
            self.logger.error(f"❌ 计算窗口特征置信度异常: {e}")
            return 0.0

    def _extract_window_text_enhanced(self, hwnd: int) -> str:
        """增强的窗口文本提取 - 专门针对错误对话框"""
        try:
            all_texts = []

            # 获取窗口标题
            title = win32gui.GetWindowText(hwnd)
            if title.strip():
                all_texts.append(title.strip())

            # 深度遍历所有子控件
            def enum_all_children(parent_hwnd, depth=0):
                if depth > 5:  # 限制递归深度
                    return

                def enum_callback(child_hwnd, _):
                    try:
                        child_text = win32gui.GetWindowText(child_hwnd)
                        child_class = win32gui.GetClassName(child_hwnd)

                        if child_text.strip():
                            all_texts.append(child_text.strip())

                            # 特别关注包含关键词的文本
                            keywords = ["操作", "频繁", "稍后", "再试", "请", "添加"]
                            if any(keyword in child_text for keyword in keywords):
                                self.logger.info(f"🎯 发现关键文本: [{child_class}] {child_text}")

                        # 递归检查子控件
                        enum_all_children(child_hwnd, depth + 1)

                    except:
                        pass
                    return True

                try:
                    win32gui.EnumChildWindows(parent_hwnd, enum_callback, 0)
                except:
                    pass

            enum_all_children(hwnd)

            combined_text = " ".join(all_texts)

            if combined_text.strip():
                self.logger.info(f"🔍 增强提取到文本: {combined_text[:300]}...")

            return combined_text

        except Exception as e:
            self.logger.error(f"❌ 增强文本提取异常: {e}")
            return ""

    def handle_frequency_error(self, detection_result: ErrorDetectionResult) -> bool:
        """处理检测到的频率错误

        精确版本，只关闭出现频繁错误的特定微信窗口：
        1. 点击错误提示窗口的"确定"按钮
        2. 精确关闭出现频繁错误的特定微信窗口
        3. 保留其他正常的微信窗口

        Args:
            detection_result: 错误检测结果

        Returns:
            bool: 处理是否成功
        """
        if not detection_result.has_error:
            self.logger.warning("⚠️ 未检测到错误，无需处理")
            return True

        self.logger.info("🚨 开始处理'操作过于频繁'错误...")
        self.logger.info(f"📋 错误类型: {detection_result.error_type}")
        self.logger.info(f"📝 错误信息: {detection_result.error_message}")

        try:
            # 记录处理历史
            with self._lock:
                self.detection_history.append(detection_result)
                if len(self.detection_history) > self.max_history_size:
                    self.detection_history.pop(0)

            # 🆕 步骤0: 记录窗口错误状态，检查是否应该终止程序
            should_terminate = False
            if detection_result.window_info:
                should_terminate = self._record_window_error(detection_result.window_info)
                if should_terminate:
                    self.logger.error("🚨 所有微信窗口都已失败，建议终止程序运行")
                    # 这里可以设置一个全局标志，让主程序知道应该终止
                    self._set_terminate_flag()

            # 🆕 步骤1: 点击错误提示窗口的"确定"按钮（优先处理）
            self.logger.info("🔄 步骤1: 点击错误提示窗口的'确定'按钮...")
            click_success = self._click_error_dialog_ok_button(detection_result)
            if click_success:
                self.logger.info("✅ 成功点击错误提示确定按钮")
                # 等待错误对话框关闭
                time.sleep(1.0)
            else:
                self.logger.warning("⚠️ 点击错误提示确定按钮失败，继续执行后续步骤")

            # 🆕 步骤2: 精确关闭出现频繁错误的特定微信窗口（按正确顺序）
            self.logger.info("🔄 步骤2: 精确关闭出现频繁错误的微信窗口...")
            if detection_result.window_info:
                self._force_close_current_wechat_window_only(detection_result.window_info)
            else:
                self.logger.warning("⚠️ 无法获取目标窗口信息，使用备用关闭方法...")
                # 备用方案：按顺序关闭
                self.logger.info("🔄 备用方案: 按顺序关闭窗口...")
                self._smart_close_add_friend_windows()  # 先关闭添加朋友窗口
                time.sleep(0.5)
                self._close_current_wechat_window_by_coordinates()  # 再关闭微信主窗口

            # 🆕 步骤3: 最终清理操作（确保所有相关窗口都已关闭）
            self.logger.info("🔄 步骤3: 执行最终清理操作...")
            self._cleanup_remaining_error_windows()

            self.logger.info("✅ 频率错误处理完成")
            self.logger.info("🔄 频率错误处理完成，需要重新开始流程")

            # 设置重新开始标志
            self._set_restart_flag()

            return True

        except Exception as e:
            self.logger.error(f"❌ 处理频率错误异常: {e}")
            self.logger.error(traceback.format_exc())
            return False

    def _force_close_current_wechat_window_only(self, target_window_info: WindowInfo):
        """只关闭当前出现频繁错误的特定微信窗口（精确版）- 按正确顺序执行"""
        try:
            self.logger.info(f"🎯 精确关闭出现频繁错误的微信窗口: {target_window_info.title}")
            self.logger.info(f"📋 目标窗口句柄: {target_window_info.hwnd}")
            self.logger.info("📋 执行顺序: 1.关闭添加朋友窗口 → 2.关闭微信主窗口 → 3.清理残留窗口")

            # 步骤1: 智能关闭添加朋友窗口
            self.logger.info("🔄 步骤1: 智能关闭添加朋友窗口...")
            self._smart_close_add_friend_windows()

            # 步骤2: 精确关闭目标微信主窗口
            self.logger.info("🔄 步骤2: 精确关闭目标微信主窗口...")
            if target_window_info.hwnd and win32gui.IsWindow(target_window_info.hwnd):
                self.logger.info(f"🎯 关闭目标微信窗口: {target_window_info.title}")
                success = self._force_close_window_enhanced(target_window_info.hwnd)
                if success:
                    self.logger.info(f"✅ 成功关闭目标微信窗口: {target_window_info.title}")
                else:
                    self.logger.warning(f"⚠️ 目标微信窗口关闭可能不完全: {target_window_info.title}")
            else:
                self.logger.warning(f"⚠️ 目标微信窗口已不存在或无效: {target_window_info.hwnd}")

            # 步骤3: 清理残留的错误对话框和相关窗口
            self.logger.info("🔄 步骤3: 清理残留的错误对话框和相关窗口...")
            self._cleanup_remaining_error_windows()

            self.logger.info(f"✅ 精确关闭完成，仅关闭了出现频繁错误的微信窗口")

        except Exception as e:
            self.logger.error(f"❌ 精确关闭微信窗口异常: {e}")

    def _smart_close_add_friend_windows(self):
        """智能关闭添加朋友窗口"""
        try:
            self.logger.info("🔍 智能搜索添加朋友窗口...")

            # 查找所有可能的添加朋友窗口
            add_friend_windows = self._find_add_friend_windows()

            if not add_friend_windows:
                self.logger.info("ℹ️ 未发现添加朋友窗口")
                return

            # 关闭找到的添加朋友窗口
            for window in add_friend_windows:
                self.logger.info(f"🎯 关闭添加朋友窗口: {window.title}")
                try:
                    # 方法1: 发送关闭消息
                    win32gui.SendMessage(window.hwnd, win32con.WM_CLOSE, 0, 0)
                    time.sleep(0.5)

                    # 验证是否关闭
                    if not win32gui.IsWindow(window.hwnd) or not win32gui.IsWindowVisible(window.hwnd):
                        self.logger.info(f"✅ 成功关闭添加朋友窗口: {window.title}")
                        continue

                    # 方法2: 强制关闭
                    self._force_close_window_enhanced(window.hwnd)
                    self.logger.info(f"✅ 强制关闭添加朋友窗口: {window.title}")

                except Exception as e:
                    self.logger.warning(f"⚠️ 关闭添加朋友窗口失败: {e}")

            # 备用方案：使用坐标点击关闭
            self.logger.info("🔄 备用方案：使用坐标点击关闭...")
            self._close_add_friend_window_by_coordinates()

        except Exception as e:
            self.logger.error(f"❌ 智能关闭添加朋友窗口异常: {e}")

    def _find_add_friend_windows(self) -> List[WindowInfo]:
        """查找添加朋友窗口"""
        try:
            add_friend_windows = []

            def enum_windows_callback(hwnd, _):
                try:
                    if not win32gui.IsWindowVisible(hwnd):
                        return True

                    title = win32gui.GetWindowText(hwnd)
                    class_name = win32gui.GetClassName(hwnd)
                    rect = win32gui.GetWindowRect(hwnd)
                    width = rect[2] - rect[0]
                    height = rect[3] - rect[1]

                    # 检查是否是添加朋友窗口
                    is_add_friend_window = (
                        # 标题包含关键词
                        any(keyword in title for keyword in ["添加朋友", "添加联系人", "Add Friend"]) or
                        # 类名和大小匹配
                        (class_name in ["Qt51514QWindowIcon", "WeChatMainWndForPC"] and
                         300 <= width <= 800 and 150 <= height <= 600)
                    )

                    if is_add_friend_window:
                        window_info = WindowInfo(
                            hwnd=hwnd,
                            title=title or "",
                            class_name=class_name,
                            rect=rect,
                            is_visible=True,
                            is_enabled=win32gui.IsWindowEnabled(hwnd)
                        )
                        add_friend_windows.append(window_info)
                        self.logger.info(f"🔍 发现添加朋友窗口: {title} ({class_name}) {width}x{height}")

                except:
                    pass
                return True

            win32gui.EnumWindows(enum_windows_callback, None)
            self.logger.info(f"📊 共发现 {len(add_friend_windows)} 个添加朋友窗口")
            return add_friend_windows

        except Exception as e:
            self.logger.error(f"❌ 查找添加朋友窗口异常: {e}")
            return []

    def _cleanup_remaining_error_windows(self):
        """清理残留的错误对话框和相关窗口"""
        try:
            self.logger.info("🧹 清理残留的错误对话框...")

            # 查找并关闭残留的错误对话框
            error_dialogs = self._find_error_dialog_windows()
            for dialog in error_dialogs:
                try:
                    self.logger.info(f"🗑️ 清理错误对话框: {dialog.title}")
                    win32gui.SendMessage(dialog.hwnd, win32con.WM_CLOSE, 0, 0)
                    time.sleep(0.2)
                except:
                    pass

            # 额外的清理操作
            self._close_error_related_windows()

        except Exception as e:
            self.logger.warning(f"⚠️ 清理残留窗口异常: {e}")

    def _record_window_error(self, window_info: WindowInfo) -> bool:
        """记录窗口错误状态，返回是否应该终止程序"""
        try:
            current_time = time.time()
            window_hwnd = window_info.hwnd

            with self._lock:
                if window_hwnd not in self.window_error_status:
                    self.window_error_status[window_hwnd] = {
                        "title": window_info.title,
                        "error_count": 0,
                        "last_error_time": 0
                    }

                # 检查是否在冷却期内
                last_error_time = self.window_error_status[window_hwnd]["last_error_time"]
                if current_time - last_error_time < self.error_cooldown_time:
                    # 在冷却期内，增加错误计数
                    self.window_error_status[window_hwnd]["error_count"] += 1
                else:
                    # 超过冷却期，重置错误计数
                    self.window_error_status[window_hwnd]["error_count"] = 1

                self.window_error_status[window_hwnd]["last_error_time"] = current_time

                error_count = self.window_error_status[window_hwnd]["error_count"]
                self.logger.info(f"📊 窗口 {window_info.title} 错误次数: {error_count}/{self.max_error_count_per_window}")

                # 检查是否达到最大错误次数
                if error_count >= self.max_error_count_per_window:
                    self.logger.warning(f"⚠️ 窗口 {window_info.title} 达到最大错误次数，标记为不可用")

                # 检查是否所有窗口都不可用
                return self._check_all_windows_failed()

        except Exception as e:
            self.logger.error(f"❌ 记录窗口错误状态异常: {e}")
            return False

    def _check_all_windows_failed(self) -> bool:
        """检查是否所有微信窗口都已失败"""
        try:
            # 获取当前所有微信窗口
            all_wechat_windows = self._find_windows_by_criteria("main_wechat")

            if not all_wechat_windows:
                self.logger.warning("⚠️ 未找到任何微信窗口")
                return True

            failed_count = 0
            for window in all_wechat_windows:
                if window.hwnd in self.window_error_status:
                    error_count = self.window_error_status[window.hwnd]["error_count"]
                    if error_count >= self.max_error_count_per_window:
                        failed_count += 1

            total_windows = len(all_wechat_windows)
            self.logger.info(f"📊 窗口状态统计: {failed_count}/{total_windows} 个窗口已失败")

            if failed_count >= total_windows:
                self.logger.error("🚨 所有微信窗口都已达到最大错误次数，建议终止程序")
                return True

            return False

        except Exception as e:
            self.logger.error(f"❌ 检查窗口状态异常: {e}")
            return False

    def get_window_error_status(self) -> dict:
        """获取窗口错误状态统计"""
        with self._lock:
            return self.window_error_status.copy()

    def _force_close_all_wechat_windows(self):
        """强制关闭所有微信相关窗口（增强版）"""
        try:
            self.logger.info("🚨 强制关闭所有微信相关窗口...")

            # 1. 关闭添加朋友窗口（使用指定坐标）
            self.logger.info("🔄 1. 关闭添加朋友窗口...")
            if not self._close_add_friend_window_by_coordinates():
                self.logger.warning("⚠️ 关闭添加朋友窗口失败，继续执行")

            # 2. 关闭当前微信窗口（使用指定坐标）
            self.logger.info("🔄 2. 关闭当前微信窗口...")
            if not self._close_current_wechat_window_by_coordinates():
                self.logger.warning("⚠️ 关闭当前微信窗口失败，继续执行")

            # 3. 查找并强制关闭所有微信窗口
            self.logger.info("🔄 3. 查找并强制关闭所有微信窗口...")
            self._force_close_all_wechat_windows_by_search()

            # 4. 关闭错误对话框
            self.logger.info("🔄 4. 关闭错误对话框...")
            self._close_error_related_windows()

        except Exception as e:
            self.logger.error(f"❌ 强制关闭所有微信窗口异常: {e}")

    def _force_close_all_wechat_windows_by_search(self):
        """通过搜索强制关闭所有微信窗口（增强安全版 - 防止误关闭）"""
        try:
            self.logger.warning("🚨 批量搜索关闭微信窗口 - 此操作有风险，请谨慎使用")
            self.logger.info("🔍 搜索所有微信窗口进行强制关闭...")

            # 🆕 增加安全检查：确认是否真的需要批量关闭
            batch_close_enabled = getattr(self, 'config', {}).get('enable_batch_window_close', False)
            if not batch_close_enabled:
                self.logger.warning("🔒 批量窗口关闭已禁用（防止误关闭），跳过此操作")
                self.logger.info("💡 如需启用，请在配置文件中设置 'enable_batch_window_close': true")
                return

            # 查找所有微信窗口
            all_wechat_windows = []

            # 查找主微信窗口（增强验证）
            main_windows = self._find_windows_by_criteria("main_wechat")
            # 🆕 二次验证：确保是真正的微信窗口
            verified_main_windows = []
            for window in main_windows:
                if self._verify_wechat_window_safety(window):
                    verified_main_windows.append(window)
                else:
                    self.logger.warning(f"🚫 跳过可疑窗口: {window.title} ({window.class_name})")
            all_wechat_windows.extend(verified_main_windows)

            # 查找添加好友窗口（增强验证）
            add_friend_windows = self._find_windows_by_criteria("add_friend")
            verified_add_friend_windows = []
            for window in add_friend_windows:
                if self._verify_wechat_window_safety(window):
                    verified_add_friend_windows.append(window)
                else:
                    self.logger.warning(f"🚫 跳过可疑添加好友窗口: {window.title} ({window.class_name})")
            all_wechat_windows.extend(verified_add_friend_windows)

            # 查找错误对话框（仅限微信相关）
            error_windows = self._find_windows_by_criteria("error_dialog")
            verified_error_windows = []
            for window in error_windows:
                if self._verify_wechat_window_safety(window):
                    verified_error_windows.append(window)
                else:
                    self.logger.warning(f"🚫 跳过非微信错误对话框: {window.title} ({window.class_name})")
            all_wechat_windows.extend(verified_error_windows)

            # 去重
            unique_windows = {}
            for window in all_wechat_windows:
                unique_windows[window.hwnd] = window

            self.logger.info(f"🎯 经过安全验证，找到 {len(unique_windows)} 个确认的微信窗口需要关闭")

            # 🆕 限制批量关闭的数量，防止意外
            max_batch_close = getattr(self, 'config', {}).get('max_batch_close_count', 5)
            if len(unique_windows) > max_batch_close:
                self.logger.error(f"🚨 发现 {len(unique_windows)} 个窗口，超过安全限制 {max_batch_close}")
                self.logger.error("🚨 为防止误操作，取消批量关闭操作")
                return

            # 强制关闭每个窗口（使用安全方法）
            for hwnd, window_info in unique_windows.items():
                self.logger.info(f"🔄 安全关闭窗口: {window_info.title} ({window_info.class_name})")

                # 🆕 使用安全的关闭方法（不包含进程终止）
                if self._safe_close_window(hwnd):
                    self.logger.info(f"✅ 成功关闭窗口: {window_info.title}")
                else:
                    self.logger.warning(f"⚠️ 无法关闭窗口: {window_info.title}")

        except Exception as e:
            self.logger.error(f"❌ 搜索关闭微信窗口异常: {e}")

    def _cleanup_remaining_windows(self):
        """清理剩余的窗口"""
        try:
            self.logger.info("🧹 执行额外清理操作...")

            # 等待一段时间让窗口完全关闭
            time.sleep(2.0)

            # 再次检查是否还有微信相关窗口
            remaining_windows = self._find_windows_by_criteria("main_wechat")
            remaining_windows.extend(self._find_windows_by_criteria("add_friend"))
            remaining_windows.extend(self._find_windows_by_criteria("error_dialog"))

            if remaining_windows:
                self.logger.warning(f"⚠️ 发现 {len(remaining_windows)} 个残留窗口，执行最终清理...")
                for window_info in remaining_windows:
                    self._force_close_window_enhanced(window_info.hwnd)
            else:
                self.logger.info("✅ 所有微信窗口已成功关闭")

        except Exception as e:
            self.logger.warning(f"⚠️ 清理剩余窗口异常: {e}")

    def _close_error_related_windows(self):
        """关闭与错误相关的窗口（简化版本）"""
        try:
            self.logger.info("🔄 关闭错误相关窗口...")

            # 尝试关闭可能的错误对话框
            error_windows = self._find_windows_by_criteria("error_dialog")
            for window_info in error_windows:
                if self._close_window(window_info.hwnd):
                    self.logger.info(f"✅ 已关闭错误窗口: {window_info.title}")

        except Exception as e:
            self.logger.warning(f"⚠️ 关闭错误相关窗口异常: {e}")
            # 不影响主流程，继续执行

    def _click_error_dialog_ok_button(self, detection_result: ErrorDetectionResult) -> bool:
        """点击错误提示窗口的确定按钮 - 增强版：智能识别错误对话框并点击确定"""
        try:
            self.logger.info("🔍 开始智能识别和点击错误对话框的确定按钮...")

            # 方法1: 尝试从detection_result中获取窗口信息
            if detection_result.window_info:
                self.logger.info(f"📋 从检测结果获取窗口信息: {detection_result.window_info.title}")
                if self._try_click_ok_in_window(detection_result.window_info):
                    return True

            # 方法2: 智能搜索所有可能的错误对话框
            self.logger.info("🔍 智能搜索错误对话框...")
            error_dialogs = self._find_error_dialog_windows()

            for dialog in error_dialogs:
                self.logger.info(f"🎯 尝试点击对话框: {dialog.title} (大小: {dialog.rect[2]-dialog.rect[0]}x{dialog.rect[3]-dialog.rect[1]})")
                if self._try_click_ok_in_window(dialog):
                    self.logger.info(f"✅ 成功点击错误对话框确定按钮: {dialog.title}")
                    return True

            # 方法3: 使用固定坐标点击（备用方案）
            self.logger.info("🔄 使用固定坐标点击方案...")
            if self._click_ok_by_fixed_coordinates():
                self.logger.info("✅ 固定坐标点击成功")
                return True

            # 方法4: 使用键盘Enter键（最后备用方案）
            self.logger.info("⌨️ 使用键盘Enter键...")
            if self._press_enter_key():
                self.logger.info("✅ 键盘Enter键成功")
                return True

            self.logger.error("❌ 所有点击确定按钮的方法都失败")
            return False

        except Exception as e:
            self.logger.error(f"❌ 点击确定按钮异常: {e}")
            import traceback
            self.logger.error(f"详细异常信息: {traceback.format_exc()}")
            return False

    def _find_error_dialog_windows(self) -> List[WindowInfo]:
        """智能查找错误对话框窗口"""
        try:
            error_dialogs = []

            def enum_windows_callback(hwnd, _):
                try:
                    if not win32gui.IsWindowVisible(hwnd):
                        return True

                    title = win32gui.GetWindowText(hwnd)
                    class_name = win32gui.GetClassName(hwnd)
                    rect = win32gui.GetWindowRect(hwnd)
                    width = rect[2] - rect[0]
                    height = rect[3] - rect[1]

                    # 检查是否是错误对话框
                    is_error_dialog = (
                        # 标题包含关键词
                        any(keyword in title for keyword in ["微信", "提示", "错误", "警告"]) or
                        # 类名匹配
                        class_name in ["#32770", "Qt51514QWindowIcon", "Dialog"] or
                        # 大小符合对话框特征
                        (250 <= width <= 500 and 100 <= height <= 300)
                    )

                    if is_error_dialog:
                        window_info = WindowInfo(
                            hwnd=hwnd,
                            title=title or "",
                            class_name=class_name,
                            rect=rect,
                            is_visible=True,
                            is_enabled=win32gui.IsWindowEnabled(hwnd)
                        )
                        error_dialogs.append(window_info)
                        self.logger.info(f"🔍 发现错误对话框: {title} ({class_name}) {width}x{height}")

                except:
                    pass
                return True

            win32gui.EnumWindows(enum_windows_callback, None)
            self.logger.info(f"📊 共发现 {len(error_dialogs)} 个可能的错误对话框")
            return error_dialogs

        except Exception as e:
            self.logger.error(f"❌ 查找错误对话框异常: {e}")
            return []

    def _try_click_ok_in_window(self, window_info: WindowInfo) -> bool:
        """尝试在指定窗口中点击确定按钮"""
        try:
            # 激活窗口
            if not self._activate_window(window_info.hwnd):
                return False

            # 查找确定按钮
            ok_button_pos = self._find_ok_button_in_window(window_info.hwnd)
            if not ok_button_pos:
                # 如果找不到确定按钮，尝试使用窗口中心点击
                rect = window_info.rect
                center_x = (rect[0] + rect[2]) // 2
                center_y = (rect[1] + rect[3]) // 2 + 20  # 稍微偏下一点
                ok_button_pos = (center_x, center_y)
                self.logger.info(f"🎯 使用窗口中心坐标: ({center_x}, {center_y})")

            # 执行点击
            return self._enhanced_click_ok_button(ok_button_pos, window_info.hwnd)

        except Exception as e:
            self.logger.warning(f"⚠️ 在窗口中点击确定按钮失败: {e}")
            return False

    def _click_ok_by_fixed_coordinates(self) -> bool:
        """使用固定坐标点击确定按钮（备用方案）"""
        try:
            # 常见的确定按钮位置
            common_positions = [
                (400, 300),  # 屏幕中心偏上
                (450, 350),  # 屏幕中心
                (500, 400),  # 屏幕中心偏下
                (350, 280),  # 左侧中心
                (550, 320),  # 右侧中心
            ]

            for pos in common_positions:
                self.logger.info(f"🎯 尝试固定坐标: {pos}")
                try:
                    pyautogui.click(pos[0], pos[1])
                    time.sleep(0.5)

                    # 简单验证：检查是否还有错误对话框
                    if len(self._find_error_dialog_windows()) == 0:
                        self.logger.info(f"✅ 固定坐标点击成功: {pos}")
                        return True

                except Exception as e:
                    self.logger.warning(f"⚠️ 固定坐标 {pos} 点击失败: {e}")
                    continue

            return False

        except Exception as e:
            self.logger.error(f"❌ 固定坐标点击异常: {e}")
            return False

    def _press_enter_key(self) -> bool:
        """按Enter键确认（最后备用方案）"""
        try:
            self.logger.info("⌨️ 按Enter键确认...")
            pyautogui.press('enter')
            time.sleep(0.5)

            # 验证是否成功
            if len(self._find_error_dialog_windows()) == 0:
                return True

            # 再试一次
            pyautogui.press('enter')
            time.sleep(0.5)
            return len(self._find_error_dialog_windows()) == 0

        except Exception as e:
            self.logger.error(f"❌ 按Enter键异常: {e}")
            return False

    def _enhanced_click_ok_button(self, button_pos: Tuple[int, int], hwnd: int) -> bool:
        """增强的确定按钮点击方法 - 支持多种点击方式和验证"""
        try:
            button_x, button_y = button_pos
            self.logger.info(f"🎯 开始增强点击操作，目标坐标: ({button_x}, {button_y})")

            # 记录点击前的窗口状态
            window_exists_before = win32gui.IsWindow(hwnd) and win32gui.IsWindowVisible(hwnd)
            self.logger.info(f"📊 点击前窗口状态: {'存在且可见' if window_exists_before else '不存在或不可见'}")

            # 方法1: pyautogui单击
            self.logger.info("🖱️ 方法1: pyautogui单击...")
            try:
                pyautogui.click(button_x, button_y)
                time.sleep(0.8)  # 等待响应

                # 验证点击效果
                if self._verify_click_success(hwnd):
                    self.logger.info("✅ 方法1成功：pyautogui单击")
                    return True

            except Exception as e:
                self.logger.warning(f"⚠️ 方法1失败: {e}")

            # 方法2: pyautogui双击
            self.logger.info("🖱️ 方法2: pyautogui双击...")
            try:
                pyautogui.doubleClick(button_x, button_y)
                time.sleep(0.8)

                if self._verify_click_success(hwnd):
                    self.logger.info("✅ 方法2成功：pyautogui双击")
                    return True

            except Exception as e:
                self.logger.warning(f"⚠️ 方法2失败: {e}")

            # 方法3: win32api点击
            self.logger.info("🖱️ 方法3: win32api点击...")
            try:
                # 移动鼠标到目标位置
                win32api.SetCursorPos((button_x, button_y))
                time.sleep(0.2)

                # 执行点击
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
                time.sleep(0.1)
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
                time.sleep(0.8)

                if self._verify_click_success(hwnd):
                    self.logger.info("✅ 方法3成功：win32api点击")
                    return True

            except Exception as e:
                self.logger.warning(f"⚠️ 方法3失败: {e}")

            # 方法4: 多次连续点击
            self.logger.info("🖱️ 方法4: 多次连续点击...")
            try:
                for i in range(3):
                    self.logger.info(f"   第{i+1}次点击...")
                    pyautogui.click(button_x, button_y)
                    time.sleep(0.3)

                    if self._verify_click_success(hwnd):
                        self.logger.info(f"✅ 方法4成功：第{i+1}次点击")
                        return True

            except Exception as e:
                self.logger.warning(f"⚠️ 方法4失败: {e}")

            # 方法5: 发送回车键（作为备选）
            self.logger.info("⌨️ 方法5: 发送回车键...")
            try:
                # 确保窗口激活
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.3)

                # 发送回车键
                win32api.keybd_event(13, 0, 0, 0)  # Enter按下
                win32api.keybd_event(13, 0, win32con.KEYEVENTF_KEYUP, 0)  # Enter释放
                time.sleep(0.8)

                if self._verify_click_success(hwnd):
                    self.logger.info("✅ 方法5成功：回车键")
                    return True

            except Exception as e:
                self.logger.warning(f"⚠️ 方法5失败: {e}")

            self.logger.error("❌ 所有点击方法都失败")
            return False

        except Exception as e:
            self.logger.error(f"❌ 增强点击操作异常: {e}")
            return False

    def _verify_click_success(self, hwnd: int) -> bool:
        """验证点击是否成功（通过检查窗口是否关闭）"""
        try:
            # 检查窗口是否仍然存在且可见
            window_still_exists = win32gui.IsWindow(hwnd) and win32gui.IsWindowVisible(hwnd)

            if not window_still_exists:
                self.logger.info("✅ 验证成功：错误提示窗口已关闭")
                return True
            else:
                self.logger.info("⚠️ 验证失败：错误提示窗口仍然存在")
                return False

        except Exception as e:
            self.logger.warning(f"⚠️ 验证点击效果异常: {e}")
            # 异常情况下假设成功，继续流程
            return True

    def _close_add_friend_windows(self) -> bool:
        """关闭所有添加朋友窗口 - 增强版，专门处理顽固窗口"""
        try:
            self.logger.info("🔄 开始关闭添加朋友窗口...")

            windows = self._find_windows_by_criteria("add_friend")
            closed_count = 0

            for window_info in windows:
                # 先尝试标准关闭方法
                if self._close_window(window_info.hwnd):
                    closed_count += 1
                    self.logger.info(f"✅ 已关闭添加朋友窗口: {window_info.title}")
                else:
                    # 标准方法失败，尝试特殊处理
                    self.logger.info(f"🔄 标准方法失败，尝试特殊处理: {window_info.title}")
                    if self._close_add_friend_window_special(window_info):
                        closed_count += 1
                        self.logger.info(f"✅ 特殊方法成功关闭窗口: {window_info.title}")
                    else:
                        self.logger.warning(f"⚠️ 所有方法都失败，跳过窗口: {window_info.title}")

            self.logger.info(f"📊 关闭添加朋友窗口统计: {closed_count}/{len(windows)}")

            # 即使部分窗口关闭失败，也认为操作基本成功
            # 因为主要目标是处理错误并切换窗口
            return True

        except Exception as e:
            self.logger.error(f"❌ 关闭添加朋友窗口异常: {e}")
            return True  # 返回True以继续后续流程

    def _close_add_friend_window_special(self, window_info: WindowInfo) -> bool:
        """特殊方法关闭添加朋友窗口 - 使用精确坐标点击关闭按钮"""
        try:
            hwnd = window_info.hwnd

            # 方法1: 使用精确坐标点击添加朋友窗口的关闭按钮 (305, 15)
            self.logger.info("   特殊方法1: 点击添加朋友窗口关闭按钮 (305, 15)...")
            try:
                # 激活窗口
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.5)

                # 使用精确坐标 (305, 15)
                close_x = 305
                close_y = 15

                self.logger.info(f"   点击添加朋友窗口关闭按钮位置: ({close_x}, {close_y})")

                # 多重点击确保成功
                for attempt in range(3):
                    self.logger.info(f"   点击尝试 {attempt + 1}/3...")

                    # 移动鼠标到位置
                    win32api.SetCursorPos((close_x, close_y))
                    time.sleep(0.2)

                    # 执行点击
                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
                    time.sleep(0.1)
                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
                    time.sleep(0.8)

                    # 检查是否关闭
                    if self._is_window_closed(hwnd):
                        self.logger.info(f"   ✅ 第{attempt + 1}次点击成功关闭添加朋友窗口")
                        return True

                # 如果win32api失败，尝试pyautogui
                self.logger.info("   尝试pyautogui点击关闭按钮...")
                pyautogui.click(close_x, close_y)
                time.sleep(0.5)
                pyautogui.click(close_x, close_y)  # 双击确保
                time.sleep(0.8)

                if self._is_window_closed(hwnd):
                    self.logger.info("   ✅ pyautogui点击成功关闭添加朋友窗口")
                    return True

            except Exception as e:
                self.logger.debug(f"   精确坐标点击失败: {e}")

            # 方法2: 尝试发送Alt+F4
            self.logger.info("   特殊方法2: 发送Alt+F4...")
            try:
                # 激活窗口
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.3)

                # 发送Alt+F4
                win32api.keybd_event(18, 0, 0, 0)  # Alt按下
                win32api.keybd_event(115, 0, 0, 0)  # F4按下
                win32api.keybd_event(115, 0, win32con.KEYEVENTF_KEYUP, 0)  # F4释放
                win32api.keybd_event(18, 0, win32con.KEYEVENTF_KEYUP, 0)  # Alt释放
                time.sleep(1.0)

                if self._is_window_closed(hwnd):
                    self.logger.info("   ✅ Alt+F4成功关闭窗口")
                    return True
            except Exception as e:
                self.logger.debug(f"   Alt+F4方法失败: {e}")

            # 方法3: 尝试ESC键
            self.logger.info("   特殊方法3: 按ESC键...")
            try:
                # 激活窗口
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.3)

                # 按ESC键
                win32api.keybd_event(27, 0, 0, 0)  # ESC按下
                win32api.keybd_event(27, 0, win32con.KEYEVENTF_KEYUP, 0)  # ESC释放
                time.sleep(1.0)

                if self._is_window_closed(hwnd):
                    self.logger.info("   ✅ ESC键成功关闭窗口")
                    return True
            except Exception as e:
                self.logger.debug(f"   ESC键方法失败: {e}")

            return False

        except Exception as e:
            self.logger.error(f"❌ 特殊关闭方法异常: {e}")
            return False

    def _close_add_friend_window_by_coordinates(self) -> bool:
        """使用指定坐标关闭添加朋友窗口"""
        try:
            self.logger.info("🎯 使用指定坐标 (1504, 13) 关闭添加朋友窗口...")

            # 指定的关闭坐标
            close_x = 1504
            close_y = 13

            # 等待窗口稳定
            time.sleep(0.5)

            # 方法1: pyautogui点击
            self.logger.info(f"🖱️ 方法1: pyautogui点击坐标 ({close_x}, {close_y})...")
            try:
                pyautogui.click(close_x, close_y)
                time.sleep(1.0)  # 等待窗口关闭
                self.logger.info("✅ pyautogui点击添加朋友窗口关闭按钮成功")
                return True
            except Exception as e:
                self.logger.warning(f"⚠️ pyautogui点击失败: {e}")

            # 方法2: win32api点击
            self.logger.info(f"🖱️ 方法2: win32api点击坐标 ({close_x}, {close_y})...")
            try:
                # 移动鼠标到目标位置
                win32api.SetCursorPos((close_x, close_y))
                time.sleep(0.2)

                # 执行点击
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
                time.sleep(0.1)
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
                time.sleep(1.0)

                self.logger.info("✅ win32api点击添加朋友窗口关闭按钮成功")
                return True
            except Exception as e:
                self.logger.warning(f"⚠️ win32api点击失败: {e}")

            # 方法3: 多次点击确保成功
            self.logger.info("🖱️ 方法3: 多次点击确保成功...")
            try:
                for i in range(3):
                    self.logger.info(f"   第{i+1}次点击...")
                    pyautogui.click(close_x, close_y)
                    time.sleep(0.5)

                self.logger.info("✅ 多次点击添加朋友窗口关闭按钮完成")
                return True
            except Exception as e:
                self.logger.warning(f"⚠️ 多次点击失败: {e}")

            return False

        except Exception as e:
            self.logger.error(f"❌ 关闭添加朋友窗口异常: {e}")
            return False

    def _close_current_wechat_window_by_coordinates(self) -> bool:
        """使用指定坐标关闭当前微信窗口"""
        try:
            self.logger.info("🎯 使用指定坐标 (700, 16) 关闭当前微信窗口...")

            # 指定的关闭坐标
            close_x = 700
            close_y = 16

            # 等待窗口稳定
            time.sleep(0.5)

            # 方法1: pyautogui点击
            self.logger.info(f"🖱️ 方法1: pyautogui点击坐标 ({close_x}, {close_y})...")
            try:
                pyautogui.click(close_x, close_y)
                time.sleep(1.0)  # 等待窗口关闭
                self.logger.info("✅ pyautogui点击当前微信窗口关闭按钮成功")
                return True
            except Exception as e:
                self.logger.warning(f"⚠️ pyautogui点击失败: {e}")

            # 方法2: win32api点击
            self.logger.info(f"🖱️ 方法2: win32api点击坐标 ({close_x}, {close_y})...")
            try:
                # 移动鼠标到目标位置
                win32api.SetCursorPos((close_x, close_y))
                time.sleep(0.2)

                # 执行点击
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
                time.sleep(0.1)
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
                time.sleep(1.0)

                self.logger.info("✅ win32api点击当前微信窗口关闭按钮成功")
                return True
            except Exception as e:
                self.logger.warning(f"⚠️ win32api点击失败: {e}")

            # 方法3: 多次点击确保成功
            self.logger.info("🖱️ 方法3: 多次点击确保成功...")
            try:
                for i in range(3):
                    self.logger.info(f"   第{i+1}次点击...")
                    pyautogui.click(close_x, close_y)
                    time.sleep(0.5)

                self.logger.info("✅ 多次点击当前微信窗口关闭按钮完成")
                return True
            except Exception as e:
                self.logger.warning(f"⚠️ 多次点击失败: {e}")

            return False

        except Exception as e:
            self.logger.error(f"❌ 关闭当前微信窗口异常: {e}")
            return False

    def _set_restart_flag(self):
        """设置重新开始标志"""
        try:
            with self._lock:
                self._restart_required = True
                self.logger.info("🔄 已设置重新开始标志")
        except Exception as e:
            self.logger.error(f"❌ 设置重新开始标志异常: {e}")

    def is_restart_required(self) -> bool:
        """检查是否需要重新开始"""
        try:
            with self._lock:
                return self._restart_required
        except Exception as e:
            self.logger.error(f"❌ 检查重新开始标志异常: {e}")
            return False

    def clear_restart_flag(self):
        """清除重新开始标志"""
        try:
            with self._lock:
                self._restart_required = False
                self.logger.info("🔄 已清除重新开始标志")
        except Exception as e:
            self.logger.error(f"❌ 清除重新开始标志异常: {e}")

    def _set_terminate_flag(self):
        """设置程序终止标志"""
        try:
            with self._lock:
                self._terminate_required = True
                self.logger.error("🚨 已设置程序终止标志 - 所有微信窗口都已失败")
        except Exception as e:
            self.logger.error(f"❌ 设置程序终止标志异常: {e}")

    def is_terminate_required(self) -> bool:
        """检查是否需要终止程序"""
        try:
            with self._lock:
                return self._terminate_required
        except Exception as e:
            self.logger.error(f"❌ 检查程序终止标志异常: {e}")
            return False

    def clear_terminate_flag(self):
        """清除程序终止标志"""
        try:
            with self._lock:
                self._terminate_required = False
                self.logger.info("🔄 已清除程序终止标志")
        except Exception as e:
            self.logger.error(f"❌ 清除程序终止标志异常: {e}")

    def get_restart_status(self) -> dict:
        """获取重新开始状态信息"""
        try:
            with self._lock:
                return {
                    "restart_required": self._restart_required,
                    "last_detection_time": self.last_detection_time,
                    "detection_count": len(self.detection_history)
                }
        except Exception as e:
            self.logger.error(f"❌ 获取重新开始状态异常: {e}")
            return {
                "restart_required": False,
                "last_detection_time": 0,
                "detection_count": 0
            }


    # ==================== 辅助方法 ====================

    def _find_windows_by_criteria(self, window_type: str) -> List[WindowInfo]:
        """根据条件查找窗口"""
        try:
            if window_type not in self.target_windows:
                return []

            criteria = self.target_windows[window_type]
            found_windows = []

            def enum_windows_callback(hwnd, _):
                try:
                    window_info = self._get_window_info(hwnd)
                    if self._match_window_criteria(window_info, criteria):
                        found_windows.append(window_info)
                except:
                    pass
                return True

            win32gui.EnumWindows(enum_windows_callback, None)
            return found_windows

        except Exception as e:
            self.logger.error(f"❌ 查找窗口异常: {e}")
            return []

    def _get_window_info(self, hwnd: int) -> WindowInfo:
        """获取窗口信息"""
        try:
            title = win32gui.GetWindowText(hwnd)
            class_name = win32gui.GetClassName(hwnd)
            rect = win32gui.GetWindowRect(hwnd)
            is_visible = win32gui.IsWindowVisible(hwnd)
            is_enabled = win32gui.IsWindowEnabled(hwnd)

            return WindowInfo(
                hwnd=hwnd,
                title=title or "",
                class_name=class_name or "",
                rect=rect,
                is_visible=bool(is_visible),
                is_enabled=bool(is_enabled)
            )
        except Exception as e:
            self.logger.error(f"❌ 获取窗口信息异常: {e}")
            return WindowInfo(hwnd, "", "", (0, 0, 0, 0), False, False)

    def _match_window_criteria(self, window_info: WindowInfo, criteria: Dict) -> bool:
        """检查窗口是否符合条件（增强版 - 防止误识别其他应用程序）"""
        try:
            # 检查窗口是否可见和启用
            if not window_info.is_visible:
                return False

            # 🆕 优先检查排除的类名（防止误识别其他应用程序）
            exclude_class_names = criteria.get("exclude_class_names", [])
            if exclude_class_names and window_info.class_name in exclude_class_names:
                self.logger.debug(f"🚫 排除窗口 {window_info.title} (类名: {window_info.class_name})")
                return False

            # 🆕 检查是否要求精确类名匹配
            require_exact_class = criteria.get("require_exact_class", False)
            if require_exact_class:
                class_names = criteria.get("class_names", [])
                if not class_names or window_info.class_name not in class_names:
                    return False
                # 对于精确匹配，还要检查进程名
                if not self._is_wechat_process(window_info.hwnd):
                    self.logger.debug(f"🚫 非微信进程窗口: {window_info.title} (类名: {window_info.class_name})")
                    return False
            else:
                # 检查类名
                class_names = criteria.get("class_names", [])
                if class_names and window_info.class_name not in class_names:
                    return False

            # 检查标题模式
            title_patterns = criteria.get("title_patterns", [])
            if title_patterns:
                title_match = any(pattern in window_info.title for pattern in title_patterns)
                if not title_match:
                    return False

            # 检查窗口大小
            size_range = criteria.get("size_range", {})
            width = window_info.rect[2] - window_info.rect[0]
            height = window_info.rect[3] - window_info.rect[1]

            if size_range:
                min_width = size_range.get("min_width", 0)
                max_width = size_range.get("max_width", 9999)
                min_height = size_range.get("min_height", 0)
                max_height = size_range.get("max_height", 9999)

                if not (min_width <= width <= max_width and min_height <= height <= max_height):
                    return False

            # 检查排除的窗口大小
            exclude_sizes = criteria.get("exclude_sizes", [])
            if exclude_sizes:
                for exclude_width, exclude_height in exclude_sizes:
                    if width == exclude_width and height == exclude_height:
                        self.logger.debug(f"🚫 排除窗口 {window_info.title} (大小: {width}x{height})")
                        return False

            return True

        except Exception as e:
            self.logger.error(f"❌ 匹配窗口条件异常: {e}")
            return False

    def _is_wechat_process(self, hwnd: int) -> bool:
        """检查窗口是否属于微信进程"""
        try:
            import psutil

            # 获取窗口的进程ID
            _, process_id = win32process.GetWindowThreadProcessId(hwnd)

            # 获取进程信息
            try:
                process = psutil.Process(process_id)
                process_name = process.name().lower()

                # 检查是否是微信进程
                wechat_process_names = ["weixin.exe", "wechat.exe", "wechatapp.exe"]
                is_wechat = any(name in process_name for name in wechat_process_names)

                if not is_wechat:
                    self.logger.debug(f"🔍 非微信进程: {process_name} (PID: {process_id})")

                return is_wechat

            except psutil.NoSuchProcess:
                self.logger.debug(f"⚠️ 进程不存在: PID {process_id}")
                return False

        except Exception as e:
            self.logger.debug(f"⚠️ 检查微信进程异常: {e}")
            # 异常情况下返回True，避免过度限制
            return True

    def _extract_window_text(self, hwnd: int) -> str:
        """提取窗口文本内容 - 简化但更可靠的版本"""
        try:
            all_texts = []

            # 方法1: 获取窗口标题
            title = win32gui.GetWindowText(hwnd)
            if title.strip():
                all_texts.append(title.strip())

            # 方法2: 递归获取所有子控件文本（深度遍历）
            child_texts = []

            def enum_child_callback(child_hwnd, depth=0):
                try:
                    # 获取子窗口文本
                    child_text = win32gui.GetWindowText(child_hwnd)
                    child_class = win32gui.GetClassName(child_hwnd)

                    if child_text.strip():
                        child_texts.append(child_text.strip())
                        # 特别记录包含关键词的文本
                        if any(keyword in child_text for keyword in ["操作", "频繁", "稍后", "再试"]):
                            self.logger.info(f"🎯 发现关键文本: [{child_class}] {child_text}")

                    # 递归遍历子窗口的子窗口
                    if depth < 3:  # 限制递归深度
                        def nested_enum_callback(nested_hwnd, _):
                            return enum_child_callback(nested_hwnd, depth + 1)
                        win32gui.EnumChildWindows(child_hwnd, nested_enum_callback, None)

                except Exception as e:
                    self.logger.debug(f"获取子窗口文本异常: {e}")
                return True

            win32gui.EnumChildWindows(hwnd, enum_child_callback, 0)

            # 合并所有文本
            if child_texts:
                all_texts.extend(child_texts)

            combined_text = " ".join(all_texts)

            # 记录提取到的文本用于调试
            if combined_text.strip():
                self.logger.info(f"🔍 提取到窗口文本: {combined_text[:200]}...")
                # 如果包含关键词，记录完整文本
                if any(keyword in combined_text for keyword in ["操作过于频繁", "请稍后再试", "操作频繁"]):
                    self.logger.info(f"� 完整关键文本: {combined_text}")
            else:
                self.logger.warning(f"⚠️ 未能提取到窗口文本，窗口句柄: {hwnd}")

            return combined_text

        except Exception as e:
            self.logger.error(f"❌ 提取窗口文本异常: {e}")
            return ""



    def _analyze_text_for_errors(self, text_content: str, window_info: WindowInfo) -> ErrorDetectionResult:
        """分析文本内容是否包含错误信息"""
        try:
            if not text_content:
                return self._create_no_error_result()

            text_lower = text_content.lower()

            # 检查各种错误模式
            for error_type, patterns in self.error_patterns.items():
                for pattern in patterns:
                    if pattern in text_content or pattern.lower() in text_lower:
                        confidence = self._calculate_confidence(pattern, text_content, window_info)

                        self.logger.info(f"🎯 检测到错误: {error_type}")
                        self.logger.info(f"📝 匹配模式: {pattern}")
                        self.logger.info(f"📊 置信度: {confidence:.2f}")
                        self.logger.info(f"📋 窗口文本: {text_content[:100]}...")

                        return ErrorDetectionResult(
                            has_error=True,
                            error_type=error_type,
                            error_message=f"检测到'{pattern}'错误提示",
                            window_info=window_info,
                            detection_time=time.time(),
                            confidence=confidence
                        )

            return self._create_no_error_result()

        except Exception as e:
            self.logger.error(f"❌ 分析文本错误异常: {e}")
            return self._create_no_error_result()

    def _calculate_confidence(self, pattern: str, text_content: str, window_info: WindowInfo) -> float:
        """计算检测置信度"""
        try:
            confidence = 0.5  # 基础置信度

            # 根据匹配模式调整置信度
            if "操作过于频繁" in pattern:
                confidence += 0.3
            elif "请稍后再试" in pattern:
                confidence += 0.2

            # 根据窗口类型调整置信度
            if "微信" in window_info.title:
                confidence += 0.1

            # 根据窗口大小调整置信度（330x194是目标大小）
            width = window_info.rect[2] - window_info.rect[0]
            height = window_info.rect[3] - window_info.rect[1]
            if width == 330 and height == 194:
                confidence += 0.1

            return min(1.0, confidence)

        except Exception as e:
            self.logger.error(f"❌ 计算置信度异常: {e}")
            return 0.5

    def _create_no_error_result(self) -> ErrorDetectionResult:
        """创建无错误结果"""
        return ErrorDetectionResult(
            has_error=False,
            error_type="none",
            error_message="未检测到错误",
            window_info=None,
            detection_time=time.time(),
            confidence=1.0
        )

    def _find_ok_button_in_window(self, hwnd: int) -> Optional[Tuple[int, int]]:
        """在窗口中查找确定按钮位置 - 支持屏幕分辨率适配"""
        try:
            # 获取窗口信息
            window_info = self._get_window_info(hwnd)
            rect = win32gui.GetWindowRect(hwnd)
            window_width = rect[2] - rect[0]
            window_height = rect[3] - rect[1]

            # 获取屏幕分辨率信息
            screen_width = win32api.GetSystemMetrics(0)  # SM_CXSCREEN
            screen_height = win32api.GetSystemMetrics(1)  # SM_CYSCREEN
            self.logger.info(f"🖥️ 当前屏幕分辨率: {screen_width}x{screen_height}")

            # 检查是否是错误提示窗口 (330x194)
            if window_width == 330 and window_height == 194 and "Weixin" in window_info.title:
                # 🔧 使用新的精确坐标，支持不同分辨率适配
                button_x, button_y = self._get_adaptive_ok_button_coordinates(
                    screen_width, screen_height, rect
                )

                self.logger.info(f"📍 使用适配坐标 (错误提示窗口): ({button_x}, {button_y})")
                self.logger.info(f"🎯 窗口信息: {window_info.title} ({window_width}x{window_height})")
                self.logger.info(f"📍 窗口位置: ({rect[0]}, {rect[1]}) 到 ({rect[2]}, {rect[3]})")
                return (button_x, button_y)

            # 其他窗口使用计算方法
            button_x = rect[0] + window_width // 2
            button_y = rect[1] + int(window_height * 0.75)

            self.logger.info(f"📍 计算确定按钮位置: ({button_x}, {button_y})")
            return (button_x, button_y)

        except Exception as e:
            self.logger.error(f"❌ 查找确定按钮异常: {e}")
            import traceback
            self.logger.error(f"详细异常信息: {traceback.format_exc()}")
            return None

    def _get_adaptive_ok_button_coordinates(self, screen_width: int, screen_height: int,
                                          window_rect: Tuple[int, int, int, int]) -> Tuple[int, int]:
        """获取适配不同屏幕分辨率的确定按钮坐标"""
        try:
            # 默认坐标（基于用户提供的新坐标）
            default_x = 1364
            default_y = 252

            # 常见分辨率的坐标映射
            resolution_coordinates = {
                (1920, 1080): (1364, 252),  # 1080p - 用户提供的新坐标
                (1366, 768): (1024, 200),   # 768p - 按比例缩放
                (1440, 900): (1080, 225),   # 900p - 按比例缩放
                (1600, 900): (1200, 225),   # 900p宽屏 - 按比例缩放
                (2560, 1440): (1819, 336),  # 1440p - 按比例缩放
                (3840, 2160): (2728, 504),  # 4K - 按比例缩放
            }

            # 检查是否有精确匹配的分辨率
            resolution_key = (screen_width, screen_height)
            if resolution_key in resolution_coordinates:
                coords = resolution_coordinates[resolution_key]
                self.logger.info(f"✅ 使用预设坐标 ({screen_width}x{screen_height}): {coords}")
                return coords

            # 如果没有精确匹配，使用比例缩放
            # 基准分辨率：1920x1080
            base_width = 1920
            base_height = 1080
            base_x = 1364
            base_y = 252

            # 计算缩放比例
            scale_x = screen_width / base_width
            scale_y = screen_height / base_height

            # 应用缩放
            scaled_x = int(base_x * scale_x)
            scaled_y = int(base_y * scale_y)

            self.logger.info(f"🔧 使用比例缩放坐标:")
            self.logger.info(f"   基准分辨率: {base_width}x{base_height}")
            self.logger.info(f"   当前分辨率: {screen_width}x{screen_height}")
            self.logger.info(f"   缩放比例: X={scale_x:.3f}, Y={scale_y:.3f}")
            self.logger.info(f"   原始坐标: ({base_x}, {base_y})")
            self.logger.info(f"   缩放坐标: ({scaled_x}, {scaled_y})")

            # 边界检查：确保坐标在屏幕范围内
            scaled_x = max(0, min(scaled_x, screen_width - 1))
            scaled_y = max(0, min(scaled_y, screen_height - 1))

            return (scaled_x, scaled_y)

        except Exception as e:
            self.logger.error(f"❌ 计算适配坐标异常: {e}")
            # 返回默认坐标作为备选
            self.logger.info(f"🔄 使用默认坐标: ({default_x}, {default_y})")
            return (default_x, default_y)

    def _activate_window(self, hwnd: int) -> bool:
        """激活窗口"""
        try:
            # 多种激活方法
            win32gui.SetForegroundWindow(hwnd)
            win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
            win32gui.SetWindowPos(hwnd, win32con.HWND_TOP, 0, 0, 0, 0,
                                win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)

            time.sleep(0.5)  # 等待激活完成

            # 验证激活是否成功
            current_hwnd = win32gui.GetForegroundWindow()
            return current_hwnd == hwnd

        except Exception as e:
            self.logger.error(f"❌ 激活窗口异常: {e}")
            return False

    def _force_close_window_enhanced(self, hwnd: int) -> bool:
        """增强的强制窗口关闭方法 - 支持更多强力关闭方式"""
        try:
            window_info = self._get_window_info(hwnd)
            self.logger.info(f"🚨 强制关闭窗口: {window_info.title} ({window_info.class_name})")

            # 方法1: 发送WM_CLOSE消息
            self.logger.info("   方法1: 发送WM_CLOSE消息...")
            win32gui.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)
            time.sleep(0.5)

            if self._is_window_closed(hwnd):
                self.logger.info("   ✅ WM_CLOSE成功关闭窗口")
                return True

            # 方法2: 发送WM_SYSCOMMAND关闭消息
            self.logger.info("   方法2: 发送系统关闭命令...")
            try:
                win32gui.PostMessage(hwnd, win32con.WM_SYSCOMMAND, win32con.SC_CLOSE, 0)
                time.sleep(0.5)

                if self._is_window_closed(hwnd):
                    self.logger.info("   ✅ 系统关闭命令成功")
                    return True
            except Exception as e:
                self.logger.debug(f"   系统关闭命令失败: {e}")

            # 方法3: 激活窗口后发送Alt+F4
            self.logger.info("   方法3: 激活窗口后发送Alt+F4...")
            try:
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.3)

                # 发送Alt+F4
                win32api.keybd_event(18, 0, 0, 0)  # Alt按下
                win32api.keybd_event(115, 0, 0, 0)  # F4按下
                win32api.keybd_event(115, 0, win32con.KEYEVENTF_KEYUP, 0)  # F4释放
                win32api.keybd_event(18, 0, win32con.KEYEVENTF_KEYUP, 0)  # Alt释放
                time.sleep(0.8)

                if self._is_window_closed(hwnd):
                    self.logger.info("   ✅ Alt+F4成功关闭窗口")
                    return True
            except Exception as e:
                self.logger.debug(f"   Alt+F4方法失败: {e}")

            # 方法4: 强制点击窗口右上角的X按钮（多次尝试）
            self.logger.info("   方法4: 强制点击窗口右上角X按钮...")
            try:
                rect = win32gui.GetWindowRect(hwnd)
                # 计算X按钮位置（通常在右上角）
                x_button_x = rect[2] - 15  # 距离右边15像素
                x_button_y = rect[1] + 15  # 距离顶部15像素

                # 激活窗口
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.3)

                # 多次点击确保成功
                for i in range(5):  # 增加点击次数
                    self.logger.info(f"   第{i+1}次强制点击X按钮: ({x_button_x}, {x_button_y})")

                    # pyautogui点击
                    pyautogui.click(x_button_x, x_button_y)
                    time.sleep(0.2)

                    # win32api点击
                    win32api.SetCursorPos((x_button_x, x_button_y))
                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
                    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
                    time.sleep(0.3)

                    if self._is_window_closed(hwnd):
                        self.logger.info(f"   ✅ 第{i+1}次点击成功关闭窗口")
                        return True

            except Exception as e:
                self.logger.debug(f"   强制点击X按钮方法失败: {e}")

            # 方法5: 使用TerminateProcess强制结束进程（最后手段 - 已禁用以防止批量关闭）
            # ⚠️ 注意：进程终止会导致该进程下的所有微信窗口同时关闭，可能影响其他正常运行的微信窗口
            # 🔒 为防止批量窗口关闭问题，此方法已被禁用
            self.logger.info("   方法5: 进程终止已禁用（防止批量关闭其他微信窗口）")
            self.logger.warning("   ⚠️ 如果前4种方法都失败，建议手动关闭窗口或重启微信")

            # 如果确实需要启用进程终止，请在配置文件中设置 "enable_process_termination": true
            enable_termination = getattr(self, 'config', {}).get('enable_process_termination', False)

            if enable_termination:
                self.logger.warning("   🚨 配置启用了进程终止，这可能导致其他微信窗口关闭")
                try:
                    import psutil

                    # 获取窗口进程ID
                    _, process_id = win32process.GetWindowThreadProcessId(hwnd)

                    # 使用psutil强制结束进程
                    process = psutil.Process(process_id)
                    process_name = process.name()

                    if "wechat" in process_name.lower() or "微信" in process_name:
                        self.logger.warning(f"   🚨 强制结束微信进程: {process_name} (PID: {process_id})")
                        self.logger.warning("   🚨 警告：这将关闭该进程下的所有微信窗口！")
                        process.terminate()
                        time.sleep(1.0)

                        if not process.is_running():
                            self.logger.info("   ✅ 强制结束进程成功")
                            return True

                except Exception as e:
                    self.logger.debug(f"   强制结束进程失败: {e}")
            else:
                self.logger.info("   ℹ️ 进程终止已禁用，跳过此方法")

            # 所有方法都失败
            self.logger.warning(f"   ⚠️ 所有强制关闭方法都失败")
            return False

        except Exception as e:
            self.logger.error(f"❌ 强制关闭窗口异常: {e}")
            return False

    def _verify_wechat_window_safety(self, window_info: WindowInfo) -> bool:
        """验证窗口是否安全可关闭（防止误关闭其他应用程序）"""
        try:
            # 检查1：验证进程名称
            if not self._is_wechat_process(window_info.hwnd):
                self.logger.debug(f"🚫 非微信进程窗口: {window_info.title}")
                return False

            # 检查2：验证窗口标题（排除明显的非微信窗口）
            suspicious_titles = ["chrome", "firefox", "edge", "notepad", "explorer", "cmd", "powershell"]
            title_lower = window_info.title.lower()
            if any(suspicious in title_lower for suspicious in suspicious_titles):
                self.logger.debug(f"🚫 可疑窗口标题: {window_info.title}")
                return False

            # 检查3：验证窗口类名（更严格的检查）
            safe_class_names = ["Qt51514QWindowIcon", "WeChatMainWndForPC", "#32770"]
            if window_info.class_name not in safe_class_names:
                self.logger.debug(f"🚫 不安全的窗口类名: {window_info.class_name}")
                return False

            # 检查4：验证窗口大小（排除异常大小的窗口）
            width = window_info.rect[2] - window_info.rect[0]
            height = window_info.rect[3] - window_info.rect[1]
            if width > 2000 or height > 1500 or width < 50 or height < 50:
                self.logger.debug(f"🚫 异常窗口大小: {width}x{height}")
                return False

            self.logger.debug(f"✅ 窗口安全验证通过: {window_info.title}")
            return True

        except Exception as e:
            self.logger.warning(f"⚠️ 窗口安全验证异常: {e}")
            return False

    def _safe_close_window(self, hwnd: int) -> bool:
        """安全的窗口关闭方法（不包含进程终止）"""
        try:
            window_info = self._get_window_info(hwnd)
            self.logger.info(f"🔄 安全关闭窗口: {window_info.title} ({window_info.class_name})")

            # 方法1: 发送WM_CLOSE消息
            self.logger.debug("   方法1: 发送WM_CLOSE消息...")
            win32gui.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)
            time.sleep(0.5)

            if self._is_window_closed(hwnd):
                self.logger.debug("   ✅ WM_CLOSE成功关闭窗口")
                return True

            # 方法2: 发送WM_SYSCOMMAND关闭消息
            self.logger.debug("   方法2: 发送系统关闭命令...")
            try:
                win32gui.PostMessage(hwnd, win32con.WM_SYSCOMMAND, win32con.SC_CLOSE, 0)
                time.sleep(0.5)

                if self._is_window_closed(hwnd):
                    self.logger.debug("   ✅ 系统关闭命令成功")
                    return True
            except Exception as e:
                self.logger.debug(f"   系统关闭命令失败: {e}")

            # 方法3: 激活窗口后发送Alt+F4
            self.logger.debug("   方法3: 激活窗口后发送Alt+F4...")
            try:
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.3)

                # 发送Alt+F4
                win32api.keybd_event(18, 0, 0, 0)  # Alt按下
                win32api.keybd_event(115, 0, 0, 0)  # F4按下
                win32api.keybd_event(115, 0, win32con.KEYEVENTF_KEYUP, 0)  # F4释放
                win32api.keybd_event(18, 0, win32con.KEYEVENTF_KEYUP, 0)  # Alt释放
                time.sleep(0.8)

                if self._is_window_closed(hwnd):
                    self.logger.debug("   ✅ Alt+F4成功关闭窗口")
                    return True
            except Exception as e:
                self.logger.debug(f"   Alt+F4方法失败: {e}")

            # 方法4: 点击窗口右上角的X按钮（限制点击次数）
            self.logger.debug("   方法4: 点击窗口右上角X按钮...")
            try:
                rect = win32gui.GetWindowRect(hwnd)
                x_button_x = rect[2] - 15
                x_button_y = rect[1] + 15

                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.3)

                # 限制点击次数，避免过度操作
                for i in range(2):  # 减少到2次点击
                    self.logger.debug(f"   第{i+1}次点击X按钮: ({x_button_x}, {x_button_y})")

                    pyautogui.click(x_button_x, x_button_y)
                    time.sleep(0.3)

                    if self._is_window_closed(hwnd):
                        self.logger.debug(f"   ✅ 第{i+1}次点击成功关闭窗口")
                        return True

            except Exception as e:
                self.logger.debug(f"   点击X按钮方法失败: {e}")

            # 所有安全方法都失败
            self.logger.warning(f"   ⚠️ 所有安全关闭方法都失败，窗口可能需要手动关闭")
            return False

        except Exception as e:
            self.logger.error(f"❌ 安全关闭窗口异常: {e}")
            return False

    def _close_window(self, hwnd: int) -> bool:
        """增强的窗口关闭方法 - 支持多种关闭方式"""
        try:
            window_info = self._get_window_info(hwnd)
            self.logger.info(f"🔄 尝试关闭窗口: {window_info.title} ({window_info.class_name})")

            # 方法1: 发送WM_CLOSE消息
            self.logger.info("   方法1: 发送WM_CLOSE消息...")
            win32gui.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)
            time.sleep(0.8)

            # 检查是否已关闭
            if self._is_window_closed(hwnd):
                self.logger.info("   ✅ WM_CLOSE成功关闭窗口")
                return True

            # 方法2: 激活窗口后按ESC键
            self.logger.info("   方法2: 激活窗口后按ESC键...")
            try:
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.3)
                # 按ESC键
                win32api.keybd_event(27, 0, 0, 0)  # ESC键按下
                win32api.keybd_event(27, 0, win32con.KEYEVENTF_KEYUP, 0)  # ESC键释放
                time.sleep(0.8)

                if self._is_window_closed(hwnd):
                    self.logger.info("   ✅ ESC键成功关闭窗口")
                    return True
            except Exception as e:
                self.logger.debug(f"   ESC键方法失败: {e}")

            # 方法3: 强制点击窗口右上角的X按钮
            self.logger.info("   方法3: 强制点击窗口右上角X按钮...")
            try:
                rect = win32gui.GetWindowRect(hwnd)
                # 计算X按钮位置（通常在右上角）
                x_button_x = rect[2] - 15  # 距离右边15像素
                x_button_y = rect[1] + 15  # 距离顶部15像素

                # 激活窗口
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.5)

                # 强制点击X按钮 - 使用多种点击方法
                self.logger.info(f"   强制点击X按钮位置: ({x_button_x}, {x_button_y})")

                # 方法3a: pyautogui点击
                pyautogui.click(x_button_x, x_button_y)
                time.sleep(0.3)

                # 方法3b: 双击确保点击
                pyautogui.doubleClick(x_button_x, x_button_y)
                time.sleep(0.5)

                if self._is_window_closed(hwnd):
                    self.logger.info("   ✅ 强制点击X按钮成功关闭窗口")
                    return True

                # 方法3c: 使用win32api点击
                self.logger.info("   尝试win32api点击...")
                win32api.SetCursorPos((x_button_x, x_button_y))
                time.sleep(0.2)
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, x_button_x, x_button_y, 0, 0)
                win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, x_button_x, x_button_y, 0, 0)
                time.sleep(0.8)

                if self._is_window_closed(hwnd):
                    self.logger.info("   ✅ win32api点击成功关闭窗口")
                    return True

            except Exception as e:
                self.logger.debug(f"   强制点击X按钮方法失败: {e}")

            # 方法4: 发送WM_SYSCOMMAND关闭消息
            self.logger.info("   方法4: 发送系统关闭命令...")
            try:
                win32gui.PostMessage(hwnd, win32con.WM_SYSCOMMAND, win32con.SC_CLOSE, 0)
                time.sleep(0.8)

                if self._is_window_closed(hwnd):
                    self.logger.info("   ✅ 系统关闭命令成功")
                    return True
            except Exception as e:
                self.logger.debug(f"   系统关闭命令失败: {e}")

            # 所有方法都失败
            self.logger.warning(f"   ⚠️ 所有关闭方法都失败")
            return False

        except Exception as e:
            self.logger.error(f"❌ 关闭窗口异常: {e}")
            return False

    def _is_window_closed(self, hwnd: int) -> bool:
        """检查窗口是否已关闭"""
        try:
            return not win32gui.IsWindow(hwnd) or not win32gui.IsWindowVisible(hwnd)
        except:
            return True  # 窗口句柄无效，说明已关闭

    def _is_wechat_window(self, window_info: WindowInfo) -> bool:
        """判断是否是微信窗口"""
        try:
            # 检查类名
            wechat_class_names = ["Qt51514QWindowIcon", "WeChatMainWndForPC"]
            if window_info.class_name not in wechat_class_names:
                return False

            # 检查标题
            wechat_titles = ["微信", "Weixin", "WeChat"]
            title_match = any(title in window_info.title for title in wechat_titles)

            return title_match

        except Exception as e:
            self.logger.error(f"❌ 判断微信窗口异常: {e}")
            return False



    # ==================== 公共接口方法 ====================

    def get_detection_history(self) -> List[ErrorDetectionResult]:
        """获取检测历史"""
        with self._lock:
            return self.detection_history.copy()

    def clear_detection_history(self):
        """清空检测历史"""
        with self._lock:
            self.detection_history.clear()
        self.logger.info("🧹 已清空检测历史")

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            history = self.detection_history.copy()

        total_detections = len(history)
        error_detections = sum(1 for result in history if result.has_error)

        error_types = {}
        for result in history:
            if result.has_error:
                error_types[result.error_type] = error_types.get(result.error_type, 0) + 1

        avg_confidence = 0.0
        if history:
            avg_confidence = sum(result.confidence for result in history) / len(history)

        return {
            "total_detections": total_detections,
            "error_detections": error_detections,
            "success_rate": (total_detections - error_detections) / max(1, total_detections),
            "error_types": error_types,
            "average_confidence": avg_confidence,
            "last_detection_time": self.last_detection_time
        }

    def set_detection_timeout(self, timeout: float):
        """设置检测超时时间"""
        self.detection_timeout = max(1.0, min(30.0, timeout))
        self.logger.info(f"⏱️ 设置检测超时时间: {self.detection_timeout}秒")

    def set_retry_attempts(self, attempts: int):
        """设置重试次数"""
        self.retry_attempts = max(1, min(10, attempts))
        self.logger.info(f"🔄 设置重试次数: {self.retry_attempts}")

    def test_ok_button_coordinates(self) -> Dict[str, Any]:
        """测试确定按钮坐标设置 - 用于验证坐标更新效果"""
        try:
            self.logger.info("🧪 开始测试确定按钮坐标设置...")

            # 获取屏幕信息
            screen_width = win32api.GetSystemMetrics(0)
            screen_height = win32api.GetSystemMetrics(1)

            # 模拟错误提示窗口矩形 (330x194)
            mock_window_rect = (100, 100, 430, 294)  # x1, y1, x2, y2

            # 测试坐标计算
            test_coords = self._get_adaptive_ok_button_coordinates(
                screen_width, screen_height, mock_window_rect
            )

            # 收集测试结果
            test_result = {
                "screen_resolution": f"{screen_width}x{screen_height}",
                "calculated_coordinates": test_coords,
                "original_coordinates": (1364, 252),
                "coordinate_difference": (
                    test_coords[0] - 1364,
                    test_coords[1] - 252
                ),
                "is_within_screen": (
                    0 <= test_coords[0] < screen_width and
                    0 <= test_coords[1] < screen_height
                ),
                "test_status": "success"
            }

            self.logger.info("📊 坐标测试结果:")
            self.logger.info(f"   屏幕分辨率: {test_result['screen_resolution']}")
            self.logger.info(f"   计算坐标: {test_result['calculated_coordinates']}")
            self.logger.info(f"   原始坐标: {test_result['original_coordinates']}")
            self.logger.info(f"   坐标差异: {test_result['coordinate_difference']}")
            self.logger.info(f"   坐标有效性: {'✅ 有效' if test_result['is_within_screen'] else '❌ 超出屏幕范围'}")

            return test_result

        except Exception as e:
            self.logger.error(f"❌ 测试坐标设置异常: {e}")
            return {
                "test_status": "failed",
                "error": str(e)
            }


# ==================== 使用示例 ====================

def example_usage():
    """使用示例 - 重构版本，专注于核心功能"""
    print("🚀 微信频率错误处理器使用示例（重构版）")
    print("=" * 60)
    print("📋 功能说明：专注于错误检测和处理，不包含窗口切换功能")

    # 创建错误处理器
    handler = FrequencyErrorHandler()

    # 🆕 测试确定按钮坐标设置
    print("\n🧪 测试确定按钮坐标设置...")
    test_result = handler.test_ok_button_coordinates()
    print(f"测试结果: {test_result}")

    # 在点击确定按钮后检测错误
    print("\n🔍 开始错误检测...")
    detection_result = handler.detect_frequency_error_after_click(timeout=5.0)

    # 如果检测到错误，进行处理
    if detection_result.has_error:
        print(f"✅ 检测到错误: {detection_result.error_type}")
        print(f"📝 错误信息: {detection_result.error_message}")
        print(f"📊 置信度: {detection_result.confidence:.2f}")

        # 处理错误（重构版：只处理错误窗口，不切换微信窗口）
        print("\n🔧 开始处理错误（重构版：只关闭错误窗口）...")
        success = handler.handle_frequency_error(detection_result)
        if success:
            print("✅ 错误窗口处理成功")
            print("💡 注意：重构版本不包含窗口切换功能")
        else:
            print("❌ 错误窗口处理失败")
    else:
        print("ℹ️ 未检测到错误")

    # 获取统计信息
    print("\n📊 获取统计信息...")
    stats = handler.get_statistics()
    print(f"统计信息: {stats}")

    print("\n" + "=" * 60)
    print("🎉 重构版示例执行完成")
    print("📝 重构说明：已移除窗口切换和主界面操作功能")


if __name__ == "__main__":
    example_usage()
